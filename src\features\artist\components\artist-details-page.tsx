"use client";
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Calendar,
  UserPlus,
  LinkIcon,
  Award,
  Loader2,
} from "lucide-react"
import { useRouter, useParams } from "next/navigation"
import { useEffect, useState, useMemo } from "react"
import { ARTIST_PROFILE_QUERY } from "@/graphql/queries"
import { Accordion, AccordionContent, AccordionTrigger } from "@/components/ui/accordion";
import { AccordionItem } from "@radix-ui/react-accordion";
import { useQuery } from '@apollo/client';
import { CreditTable } from "@/components/CreditTable";
import { useInfinitePerformedSongs } from "../hooks/use-infinite-performed-songs";
import { useIntersectionObserver } from "../hooks/use-intersection-observer";


export default function ArtistDetailsPage({ artistId }: { artistId?: string }) {
  const router = useRouter();
  const params = useParams();
  const idFromParams = params?.id as string | undefined;
  const effectiveArtistId = artistId || idFromParams;

  // Apollo Client query
  const { data: apolloData, loading, error } = useQuery(ARTIST_PROFILE_QUERY, {
    variables: {
      artistId: effectiveArtistId,
    },
    skip: !effectiveArtistId, // Skip query if no artistId
  });

  const [categorizedSongs, setCategorizedSongs] = useState<Record<string, Array<{ id: string; title?: string | null; duration?: number | null; releaseDate?: string | null; type?: 'song' | 'recording' }>>>({});
  const [openRoles, setOpenRoles] = useState<string[]>([]);

  // Process Apollo data
  const artistConnection = apolloData?.artistsConnection || null;
  const artist = artistConnection?.edges?.[0]?.node || null;

  // Infinite performed songs hook - only run after main artist data is loaded
  const {
    performedSongs,
    loading: performedLoading,
    error: performedError,
    hasNextPage: performedHasNextPage,
    loadMore: loadMorePerformed,
    totalCount: performedTotalCount,
    isLoadingMore: isLoadingMorePerformed,
  } = useInfinitePerformedSongs(artist ? effectiveArtistId || '' : '');

  // Process songs and recordings from creditedOnSongConnection and creditedOnRecordingConnection
  const songs = useMemo(() => {
    const songEdges = artist?.creditedOnSongConnection?.edges || [];
    const recordingEdges = artist?.creditedOnRecordingConnection?.edges || [];

    // Normalize song credits
    const songCredits = songEdges.map((edge: {
      role?: string;
      node?: {
        mbid?: string;
        title?: string;
        coverImage?: string;
        recordings?: Array<{
          mbid?: string;
          title?: string;
          duration?: number;
          recordID?: string;
          releaseDate?: string;
          coverImage?: string;
        }>;
      };
    }) => {
      const songMbid = edge.node?.mbid;
      const idToUse = songMbid || Math.random().toString();
      return {
        id: idToUse,
        title: edge.node?.title || null,
        duration: edge.node?.recordings?.[0]?.duration || null,
        recordID: edge.node?.recordings?.[0]?.recordID || null,
        releaseDate: edge.node?.recordings?.[0]?.releaseDate || null,
        coverPhoto: edge.node?.coverImage || edge.node?.recordings?.[0]?.coverImage || null,
        role: edge.role || null,
        type: 'song',
        credits: [{
          artistId: artist?.mbid || '',
          role: edge.role || '',
          name: artist?.name || artist?.artistName || null,
        }]
      };
    });

    // Normalize recording credits
    const recordingCredits = recordingEdges.map((edge: {
      role?: string;
      node?: {
        mbid?: string;
        title?: string;
        duration?: number;
        recordID?: string;
        releaseDate?: string;
        coverImage?: string;
      };
    }) => {
      const recordingMbid = edge.node?.mbid;
      const idToUse = recordingMbid || Math.random().toString();
      return {
        id: idToUse,
        title: edge.node?.title || null,
        duration: edge.node?.duration || null,
        recordID: edge.node?.recordID || null,
        releaseDate: edge.node?.releaseDate || null,
        coverPhoto: edge.node?.coverImage || null,
        role: edge.role || null,
        type: 'recording',
        credits: [{
          artistId: artist?.mbid || '',
          role: edge.role || '',
          name: artist?.name || artist?.artistName || null,
        }]
      };
    });

    // Combine both arrays
    return [...songCredits, ...recordingCredits];
  }, [artist]);

  // Process albums data
  const albums = useMemo(() => {
    return (artist?.albums || []).map((album: {
      mbid?: string;
      title?: string;
      releaseDate?: string;
      coverArtData?: string;
    }) => ({
      id: album.mbid || Math.random().toString(),
      title: album.title || null,
      releaseDate: album.releaseDate || null,
      coverArtData: album.coverArtData || null,
    }));
  }, [artist]);



  /**
 * Categorizes songs by the given artist's roles.
 * @param songs Array of song objects
 * @param artistId The artist's ID to filter by
 * @returns Object mapping role to array of songs
 */
function categorizeSongsByRole(
  songs: Array<{
    id: string;
    title?: string | null;
    duration?: number | null;
    releaseDate?: string | null;
    type?: 'song' | 'recording';
    credits?: Array<{
      artistId: string;
      role: string;
      name?: string | null;
    }>;
  }> = [],
  artistId: string
) {
  const result: Record<string, Array<{ id: string; title?: string | null; duration?: number | null; releaseDate?: string | null; type?: 'song' | 'recording' }>> = {};

  songs.forEach(song => {
    if (!song.credits) return;
    song.credits.forEach(credit => {
      if (credit.artistId === artistId && credit.role) {
        if (!result[credit.role]) {
          result[credit.role] = [];
        }
        result[credit.role].push({
          id: song.id,
          title: song.title,
          duration: song.duration,
          releaseDate: song.releaseDate,
          type: song.type,
        });
      }
    });
  });

  return result;
}


  useEffect(() => {
    if (!songs || !artist?.mbid) return;

    const categorized = categorizeSongsByRole(songs, artist.mbid);
    setCategorizedSongs(categorized);

    // Open all accordions by default
    const allRoles = Object.keys(categorized);
    const discographyRole = albums.length > 0 ? ['discography'] : [];
    setOpenRoles([...discographyRole, ...allRoles]);
  }, [songs, artist, albums.length])




  const handleAlbumClick = (albumId : string) => {
    router.push(`/album/?id=${albumId}`);
  };
  const handleSongClick = (songId: string) => {
    router.push(`/song/?id=${songId}`);
  }
  const handleRecordingClick = (recordingId: string) => {
    router.push(`/recording/?id=${recordingId}`);
  };
  const handleAccordionChange = (values: string[]) => {
    setOpenRoles(values);
  };




  const handleCreditRowClick = (row: { id: string; type?: "song" | "recording" }) => {
    if (row.type === "song") {
      handleSongClick(row.id);
    } else {
      handleRecordingClick(row.id);
    }
  };

  const handlePerformedRowClick = (row: { id: string; type?: "song" | "recording" }) => {
    handleRecordingClick(row.id);
  };

  // Intersection observer for infinite scroll in performed section
  const loadMorePerformedRef = useIntersectionObserver({
    onIntersect: loadMorePerformed,
    enabled: performedHasNextPage && !isLoadingMorePerformed && !performedLoading
  });

  if (loading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center text-red-500">
        Error loading data: {error.message}
      </div>
    );
  }

  if (!artist) {
    return <div className="min-h-screen flex items-center justify-center text-red-500">Artist not found</div>;
  }
  
function getCoverArtUrl(coverArtData?: string | null) {
  try {
    const parsed = JSON.parse(coverArtData || "");
    const innerParsed = typeof parsed === "string" ? JSON.parse(parsed) : parsed;
  return  innerParsed.thumbnails['250'] || innerParsed.image_url || "/dummy-image.png?height=150&width=150";
  } catch {
    return "/dummy-image.png?height=150&width=150";
  }
}






  return (
    <div className="min-h-screen bg-background">

      {/* Profile Header */}
{/* Artist Intro / Profile Header */}
<div className="px-4 md:px-8 lg:px-16 mt-6">
  <Card className="p-6 md:p-8">
    <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
      {/* Avatar */}
      <Avatar className="w-32 h-32 md:w-40 md:h-40 border border-border shadow-sm relative overflow-hidden">
        {/* Blurred background */}
        <div
          className="absolute inset-0 bg-cover bg-center blur-md scale-110"
          style={{
            backgroundImage: `url(${artist.profileImage || "/dummy-image.png"})`
          }}
        />
        <AvatarImage
          src={artist.profileImage || "/dummy-image.png"}
          alt="Artist Profile"
          className="object-contain w-full h-full relative z-10"
        />
        <AvatarFallback className="text-2xl">
          {(artist.name || artist.artistName)?.[0]?.toUpperCase() ?? "A"}
        </AvatarFallback>
      </Avatar>
        

      {/* Artist Info */}
      <div className="flex-1 space-y-4">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold leading-tight">
            {artist.name || artist.artistName}
          </h1>
           <p className="text-muted-foreground leading-relaxed">
              {artist?.bio && artist.bio }
            </p>
          <div className="flex flex-wrap items-center gap-4 text-muted-foreground mt-2">
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              <span>
                Active since{" "}
                {artist.formedDate
                  ? new Date(artist.formedDate).getFullYear()
                  : "Unknown"}
              </span>
            </div>
            {artist.disbandedDate && (
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>
                  Disbanded {new Date(artist.disbandedDate).getFullYear()}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Genres */}
        {artist.genres && artist.genres.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {artist.genres.map((genre: string, index: number) => (
              <Badge key={index} variant="secondary">
                {genre}
              </Badge>
            ))}
          </div>
        )}

        {/* Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button>
            <UserPlus className="w-4 h-4 mr-2" />
            Follow
          </Button>
          <Button variant="outline">
            <LinkIcon className="w-4 h-4 mr-2" />
            Connect
          </Button>
          {/* <Button variant="outline">
            <Edit3 className="w-4 h-4 mr-2" />
            Edit
          </Button> */}
        </div>
      </div>
    </div>
  </Card>
</div>



      <div className="px-4 md:px-8 lg:px-16 py-8 space-y-8">
        {/* About Section */}
        {/* <Card>
          <CardHeader>
            <CardTitle>About</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed">
              {artist.bio || "No biography available for this artist."}
            </p>
          </CardContent>
        </Card> */}

        {/* Experience / Discography Section */}
        {albums.length > 0 && (
          <div className="mt-8">
            <Accordion
              type="multiple"
              className="w-full"
              value={openRoles}
              onValueChange={handleAccordionChange}
            >
              <AccordionItem
                value="discography"
                className="border rounded-lg bg-card shadow-sm mb-8"
              >
                <AccordionTrigger className="px-6 py-4 text-lg font-semibold flex items-center gap-2">
                  <div>
                    Discography
                    <span className="ml-2 text-xs text-muted-foreground">
                      ({albums.length})
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="grid grid-cols-3 md:grid-cols-6 gap-4">
                    {albums.map((album: {
                      id: string;
                      title?: string | null;
                      releaseDate?: string | null;
                      coverArtData?: string | null;
                    }) => (
                      <div
                        key={album.id}
                        className="space-y-2 cursor-pointer"
                        onClick={() => handleAlbumClick(album.id)}
                      >
                        <div className="relative w-[150px] h-[150px] rounded-md overflow-hidden cursor-pointer">
                          {/* Blurred background */}
                          <div
                            className="absolute inset-0 bg-cover bg-center blur-md scale-110"
                            style={{
                              backgroundImage: `url(${getCoverArtUrl(album.coverArtData)})`
                            }}
                          />
                          {/* Main image */}

                          <Image
                            src={getCoverArtUrl(album.coverArtData)}
                            alt={`Album ${album.title || "Unknown"}`}
                            width={150}
                            height={150}
                            className="object-contain w-full h-full relative z-10"
                          />
                        </div>
                        <p className="font-medium">{album.title || "Unknown Album"}</p>
                        <p className="text-sm text-muted-foreground">
                          {album.releaseDate
                            ? new Date(album.releaseDate).getFullYear()
                            : "Unknown Year"}
                        </p>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        )}

        {/* Performed By Section */}
        {(performedSongs.length > 0 || performedLoading) && (
          <div className="mt-8">
            <div className="flex items-center gap-2 mb-4">
              <Award className="w-5 h-5" />
              <div className="flex flex-col">
                <span className="text-xl font-semibold">
                  Performed
                  {performedTotalCount > 0 && (
                    <span className="ml-2 text-sm text-muted-foreground font-normal">
                      ({performedSongs.length}/{performedTotalCount})
                    </span>
                  )}
                </span>
                {/* Progress bar */}
                {/* {performedTotalCount > 0 && (
                  <div className="mt-2 w-48">
                    <div className="flex justify-between text-xs text-muted-foreground mb-1">
                      <span>Loaded</span>
                      <span>{Math.round((performedSongs.length / performedTotalCount) * 100)}%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-1.5">
                      <div
                        className="bg-primary h-1.5 rounded-full transition-all duration-300 ease-out"
                        style={{ width: `${Math.min((performedSongs.length / performedTotalCount) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                )} */}
              </div>
            </div>
            <Card>
              <CardContent className="p-0">
                <div className="h-96 overflow-y-auto">
                  {performedLoading && performedSongs.length === 0 ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span>Loading performed songs...</span>
                      </div>
                    </div>
                  ) : performedError ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center text-red-500">
                        <p>Error loading performed songs</p>
                        <p className="text-sm">{performedError.message}</p>
                      </div>
                    </div>
                  ) : (
                    <div className="p-6">
                      <CreditTable
                        rows={performedSongs}
                        onRowClick={handlePerformedRowClick}
                      />

                      {/* Load More Trigger */}
                      {performedHasNextPage && (
                        <div ref={loadMorePerformedRef} className="flex flex-col items-center py-4 space-y-2">
                          {isLoadingMorePerformed ? (
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <Loader2 className="w-4 h-4 animate-spin" />
                              <span>Loading more songs...</span>
                            </div>
                          ) : (
                            <div className="text-muted-foreground text-sm">
                              Scroll down to load more songs
                            </div>
                          )}
                          {/* <div className="text-xs text-muted-foreground">
                            Showing {performedSongs.length} of {performedTotalCount} songs
                          </div> */}
                        </div>
                      )}

                      {/* Show completion message when all songs are loaded */}
                      {!performedHasNextPage && performedSongs.length > 0 && performedTotalCount > 0 && (
                        <div className="flex justify-center py-4">
                          <div className="text-sm text-muted-foreground">
                            All {performedTotalCount} songs loaded
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Credits Section */}
        <div className="mt-8">
          <div className="flex items-center gap-2 mb-4">
            <Award className="w-5 h-5" />
            <span className="text-xl font-semibold">Credits</span>
          </div>
          {Object.keys(categorizedSongs).length === 0 ? (
            <div className="italic text-muted-foreground px-4 py-2">
              {songs.length > 0
                ? `No credits found for this artist (${songs.length} songs available)`
                : "No songs available for this artist"
              }
            </div>
          ) : (
            <div className="space-y-4">
              <Accordion
                type="multiple"
                className="w-full"
                value={openRoles}
                onValueChange={handleAccordionChange}
              >
                {Object.entries(categorizedSongs).map(([role, songs]) => (
                  <AccordionItem
                    value={role}
                    key={role}
                    className="border rounded-lg bg-card shadow-sm mb-8"
                  >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold flex items-center gap-2">
                      <div>
                        {role.charAt(0).toUpperCase() + role.slice(1)}
                        <span className="ml-2 text-xs text-muted-foreground">
                          ({songs.length})
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 pb-4">
                      {songs.length > 0 ? (
                        <CreditTable
                          rows={songs}
                          onRowClick={handleCreditRowClick}
                        />
                      ) : (
                        <div className="italic text-muted-foreground px-4 py-2">
                          (No songs found)
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          )}
        </div>
       
      </div>
    </div>
  )
}
