"use client"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Play,
  Pause,
  Heart,
  Calendar,
  Music,
  Award,
} from "lucide-react"
import { useRouter } from "next/navigation"
import { useMusicPlayer } from "@/contexts/music-player-context/music-player-context"
import { SONG_QUERY } from "@/graphql/queries"
import { useQuery } from '@apollo/client'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"



export default function SongPage({ songId }: { songId?: string }) {
  const router = useRouter();
  const { playSong, currentSong, isPlaying, togglePlay } = useMusicPlayer();

  // Apollo Client query
  const { data: apolloData, loading, error } = useQuery(SONG_QUERY, {
    variables: { songId },
    skip: !songId, // Skip query if no songId
  });

  // Process Apollo data from connection structure
  const songConnection = apolloData?.songsConnection || null;
  const songDetails = songConnection?.edges?.[0]?.node || null;

  const handlePlayClick = () => {
    if (!songDetails) return;
    const firstRecording = songDetails.recordings?.[0];
    if (currentSong?.id === songDetails.mbid) {
      togglePlay();
    } else {
      playSong({
        id: songDetails.mbid || "",
        title: songDetails.title ?? "",
        artist: (songDetails.creditedOnSong?.[0]?.name || songDetails.creditedOnSong?.[0]?.artistName) ?? "Unknown Artist",
        album: "",
        albumArt: (songDetails.coverImage || firstRecording?.coverImage) ?? "/dummy-image.png",
        duration: firstRecording?.duration ?? 0,
        audioSrc: "https://sample-playlist.s3.us-east-2.amazonaws.com/playsample.wav?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOb%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMiJHMEUCIQD8Cn4MDm0DLXqTxUyRpLPSL5xCqx4g9RaYB2eww6uHmAIgSw7cn8J8i1sgIWuv3qr15OwgaT7GqmhrV7%2Fa2FqmQTkqsAQI%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARAAGgw0MDI5NjEzOTgxNTgiDIlkdN1QJqRyF0YY1iqEBJIbpQUiEWC15tK5aZCtffttuzABvmMLp%2FOmXRngoZBEQ92pK7HVMkXHB7vcknviiz5Y6ZilJy7aXfv50Bev1FUJk5qVrT5z2M1hfkKRgkfyroVZpEpWd4zCdNL7Ln0pKWP%2B1iyo6wf8mU%2BeKOeT5gklOwGqaqVOPQHRq3qXWaTQggMm%2FaDaoKYW97dpiBa1dhXkrHcClAFFNDGumCnfPXphRuowhEju45IMFGSeABfu6t1bKq8VfstA03bDpgd5T2zgv%2B5xah1Wx%2B1OyfRHHPY1LpgKom8cW69fNqPF%2FMdt4B7lHvgq6T%2BkIfWci661KZOMPAyUxyhVzC4Aeo8rRIawzSsyCCUlPHnLzFgdldnSfjeSwxslow5dqyMwmleFHG%2FIp5DGt9yZ7Bcp2%2BkoJdBctV%2BxmFHKDMeDepyA0tlJIzaLIObqSABw9iepBsSArjVvo5rIYt5BbwP5ivV9c4jBoRcJWvubdnRnMO8NN3wxmYqBgck7pyWhf82%2Bmu3VJkNE4cxabGTLZL4d%2FXWtxsmfLHpcyQHAUhv%2FUXMrCH4mQh4X4xKAlmZbcyalXDNhRMYzKWnTDuTc1vGJeZhFXBctfqOXJzqjDhbv6XDhN42zqbJI9wAcO4CrFk2rxUQFgIt%2FuCuUmk565XbimcFVL170qpHvaEXNnrWrPY9Npi8UEW%2Fs4zCw5IHEBjrFAkKpbjvySi7gUO%2Bae5AMUWtwX1S93NXYNtvF4ua%2FcHyKiUYhR10bJ%2BN323DuONzov1zHYuqNdpaN0qftUJSQNhDJi32QfOEiqx8USCRHb2UECLm%2F3%2Fn2jSUVYNONppW6C3SoM%2B9lKcoh0qmfHxdKZbA8RqEmF1a5kB7t1Ph7pilyaZT67vwcjUE7%2Fken4EZfD65ZSRT%2FGFxFXUp6Lbc4kBCwKZsCtCwPheFv6ptO85jN%2BKybT8fze6zw8UW0a7nlztzZS1vMFPVjG2ISwPbK2dunab6UBwK5xzNSwcE1IlYh5rFNszAbypHKl7Le8I6l2D4wSacxh06nPF51HMRlNLJhqGNhQoXutUN4TL%2FXMghvgYodhRIqezmZhLdx2IznJ4wqVE%2FGmC5TlS4vXJMsjDy%2FVN1vbYuOABAHAHswGi1q4febp%2BA%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAV3US67GHNR4ESHTO%2F20250723%2Fus-east-2%2Fs3%2Faws4_request&X-Amz-Date=20250723T054211Z&X-Amz-Expires=43200&X-Amz-SignedHeaders=host&X-Amz-Signature=010e566a80ce7e00cb8037c9012fb26f1764ac72e184029c5f04c45a09331219",
        credits: {} as { producer?: string; writer?: string; engineer?: string },
      });
    }
  };

  // const handleArtistClick = () => {
  //   router.push(`/artist`);
  // };
    const handleRecordingClick = (id: string | undefined) => {
      if(id){
        router.push(`/recording/?id=${id}`);
      }
    };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="px-4 md:px-8 lg:px-16 py-8">
          {/* Header / Hero Section Skeleton */}
          <div className="flex flex-col lg:flex-row gap-8 mb-8">
            {/* Album Cover Skeleton */}
            <div className="flex-shrink-0">
              <Skeleton className="w-[300px] h-[300px] rounded-lg" />
            </div>
            {/* Song Info Skeleton */}
            <div className="flex-1 space-y-6">
              <div className="space-y-4">
                <Skeleton className="h-12 w-2/3" />
              </div>
              {/* Action Buttons Skeleton */}
              <div className="flex flex-wrap gap-3">
                <Skeleton className="h-12 w-28" />
                <Skeleton className="h-12 w-28" />
              </div>
              {/* Song Metadata Skeleton */}
              <div className="flex flex-wrap items-center gap-6">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-24" />
                <Skeleton className="h-6 w-28" />
              </div>
            </div>
          </div>

          {/* Recordings and Credits Skeleton */}
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Recordings Skeleton */}
            <Card className="flex-1">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-semibold flex items-center gap-2">
                  <Music className="w-5 h-5" />
                  <Skeleton className="h-6 w-32" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center gap-6 py-2">
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-5 w-20" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                ))}
              </CardContent>
            </Card>
            {/* Credits Skeleton */}
            <Card className="flex-1">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  <Skeleton className="h-6 w-24" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 h-[25rem] overflow-y-auto">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between py-2">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-8 w-8 rounded-full" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  if (error) {
    return <div className="p-8 text-center text-red-500">Error loading song: {error.message}</div>;
  }

  if (!songDetails) {
    return <div className="p-8 text-center text-red-500">Song not found</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header / Hero Section */}
        <div className="flex flex-col lg:flex-row gap-8 mb-8">
          {/* Album Cover */}
          <div className="flex-shrink-0">
            <Image
              src={(songDetails?.coverImage || songDetails?.recordings?.[0]?.coverImage) ?? "/dummy-image.png"}
              alt="Song Cover"
              width={300}
              height={300}
              className="rounded-lg shadow-lg"
            />
          </div>

          {/* Song Info */}
          <div className="flex-1 space-y-6">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl font-bold">{songDetails?.title ?? "Unknown Title"}</h1>
              <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{songDetails?.recordings?.[0]?.releaseDate ?? "Unknown"}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Music className="w-4 h-4" />
                  <span>
                    {songDetails?.recordings?.[0]?.duration
                      ? `${Math.floor((songDetails.recordings[0].duration / 1000) / 60)}:${String(
                          Math.floor((songDetails.recordings[0].duration / 1000) % 60)
                        ).padStart(2, "0")}`
                      : "Unknown"}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              {songDetails?.mbid === "5467ebe1-d0cf-4b6d-b4fb-18ad74be40fe" && (
                <Button size="lg" className="gap-2" onClick={handlePlayClick}>
                  {currentSong?.id === songDetails?.id && isPlaying ? (
                    <>
                      <Pause className="w-5 h-5" />
                      Pause
                    </>
                  ) : (
                    <>
                      <Play className="w-5 h-5" />
                      Play
                    </>
                  )}
                </Button>
              )}
              <Button variant="outline" size="lg" className="gap-2">
                <Heart className="w-5 h-5" />
                Like
              </Button>
              {/* <Button variant="outline" size="lg" className="gap-2">
                <Plus className="w-5 h-5" />
                Add to Playlist
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Edit3 className="w-5 h-5" />
                Edit
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Share2 className="w-5 h-5" />
                Share
              </Button> */}
            </div>

            {/* Song Metadata */}
            <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
              {/* <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>
                  {songDetails?.duration
                    ? `${Math.floor(songDetails.duration / 60)}:${String(songDetails.duration % 60).padStart(2, "0")}`
                    : "N/A"}
                </span>
              </div> */}
              {/* <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{songDetails?.releaseDate ?? "Unknown Date"}</span>
              </div> */}
              {/* <div className="flex items-center gap-2">
                <Music className="w-4 h-4" />
                <span>ISRC: {songDetails?.recordID ?? "N/A"}</span>
              </div> */}
            </div>

            {/* Genres */}
            {/* <div className="flex flex-wrap gap-2">
              <Badge variant="secondary">Synthwave</Badge>
              <Badge variant="secondary">Pop</Badge>
              <Badge variant="secondary">Electronic</Badge>
            </div> */}
          </div>
        </div>

        {/* Recordings and Credits Row/Column Responsive */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Recordings */}
          <Card className="flex-1">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Music className="w-5 h-5" />
                Recordings
              </CardTitle>
            </CardHeader>
            <CardContent
              className={
                songDetails?.recordings && songDetails.recordings.length > 0
                  ? "space-y-6 "
                  : "flex flex-col justify-center items-center min-h-[12rem]"
              }
            >
              {songDetails?.recordings && songDetails.recordings.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Release Date</TableHead>
                      {/* <TableHead>Actions</TableHead> */}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {(songDetails.recordings || []).map((rec: {
                      mbid?: string;
                      title?: string;
                      duration?: number;
                      releaseDate?: string;
                    }) => (
                      <TableRow
                        key={rec?.mbid}
                        className="cursor-pointer"
                        onClick={() => handleRecordingClick(rec?.mbid)}
                      >
                        <TableCell>{rec?.title ?? "Untitled"}</TableCell>
                        <TableCell>
                          {rec?.duration
                            ? `${Math.floor(rec?.duration / 60000)}:${String(Math.floor((rec?.duration % 60000) / 1000)).padStart(2, "0")}`
                            : "N/A"}
                        </TableCell>
                        <TableCell>{rec?.releaseDate ?? "N/A"}</TableCell>
                        {/* <TableCell>
                          <Button size="sm" onClick={() => {}}>
                            <Play className="w-4 h-4" />
                          </Button>
                        </TableCell> */}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center w-full">
                  <Music className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No recordings available</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Credits */}
          <Card className="flex-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5" />
                Credits
              </CardTitle>
            </CardHeader>
            <CardContent
              className={
                songDetails?.creditedOnSongConnection?.edges?.length > 0
                  ? "space-y-4"
                  : "flex flex-col justify-center items-center min-h-[12rem]"
              }
            >
              <div className="grid gap-4 w-full">
                {songDetails?.creditedOnSongConnection?.edges?.length > 0 ? (
                  (() => {
                    type Role = { role: string; as?: string };
                    type Person = {
                      mbid?: string;
                      name: string;
                      profileImage?: string;
                      roles: Role[];
                      links?: string;
                    };
                    type CreditEdge = {
                      role: string;
                      as?: string;
                      node: {
                        mbid?: string;
                        name?: string;
                        artistName?: string;
                        profileImage?: string;
                        links?: string;
                      };
                    };
                    const grouped: Record<string, Person> = (songDetails.creditedOnSongConnection.edges || []).reduce(
                      (acc: Record<string, Person>, edge: CreditEdge) => {
                        const person = edge.node;
                        const name = person?.name || person?.artistName || "Unknown Artist";
                        if (!acc[name]) {
                          acc[name] = {
                            mbid: person?.mbid,
                            name,
                            profileImage: person?.profileImage,
                            roles: [],
                            links: person?.links,
                          };
                        }
                        acc[name].roles.push({ role: edge.role, as: edge.as });
                        return acc;
                      },
                      {}
                    );
                    return Object.values(grouped).map((person, idx) => (
                      <div
                        key={person.mbid ?? idx}
                        className="flex items-start gap-4 p-3 rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => router.push(`/artist/?id=${person.mbid}`)}
                      >
                        {person.profileImage && person.profileImage.trim() ? (
                          <Image
                            src={person.profileImage.trim().startsWith('http') ? person.profileImage.trim() : `/${person.profileImage.trim()}`}
                            alt={person.name}
                            width={48}
                            height={48}
                            className="rounded-full object-cover border"
                          />
                        ) : (
                          <Avatar className="w-12 h-12">
                            <AvatarFallback className="text-sm font-medium">
                              {person.name
                                .split(' ')
                                .map(word => word.charAt(0).toUpperCase())
                                .slice(0, 2)
                                .join('')}
                            </AvatarFallback>
                          </Avatar>
                        )}
                        <div className="flex-1">
                          <p className="font-medium text-primary">
                            {person.name}
                          </p>
                          <ul className="text-sm text-muted-foreground capitalize list-disc ml-4">
                            {person.roles.map((r, i) => (
                              <li key={i}>{r.role}{r.as ? ` (${r.as})` : ""}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    ));
                  })()
                ) : (
                  <div className="text-muted-foreground text-sm text-center w-full">No credits available</div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
