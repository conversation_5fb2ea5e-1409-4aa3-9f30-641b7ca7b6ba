'use client'
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Play,
  Calendar,
  Music,
  MoreHorizontal,
  Disc,
} from "lucide-react"
import { useRouter } from "next/navigation"
import { ALBUM_QUERY } from "@/graphql/queries"
import { useQuery } from '@apollo/client';

// Types for Apollo response
type AlbumTrack = {
  mbid?: string;
  title?: string;
  duration?: number;
  recordID?: string;
  releaseDate?: string;
  coverImage?: string;
}

type AlbumCreator = {
  mbid?: string;
  name?: string;
  artistName?: string;
}

export default function AlbumPage({ albumId }: { albumId?: string }) {
  const router = useRouter();

  // Apollo Client query
  const { data: apolloData, loading, error } = useQuery(ALBUM_QUERY, {
    variables: { albumId },
    skip: !albumId, // Skip query if no albumId
  });

  // Process Apollo data from connection structure
  const albumConnection = apolloData?.albumsConnection || null;
  const album = albumConnection?.edges?.[0]?.node || null;

  const formatDuration = (duration?: number | string | null) => {
    if (!duration) return "-";
    const millis = typeof duration === "string" ? parseInt(duration, 10) : duration;
    if (isNaN(millis)) return "-";
    const secs = Math.floor(millis / 1000);
    const min = Math.floor(secs / 60);
    const sec = secs % 60;
    return `${min}:${sec.toString().padStart(2, "0")}`;
  };

  // Helper function to parse cover art data and get appropriate image URL
  const getCoverArtUrl = (coverArtData?: string | null): string => {
    if (!coverArtData) return "/dummy-image.png?height=300&width=300";

    try {
      const parsed = JSON.parse(coverArtData);
      // Use 500px thumbnail for album covers, fallback to image_url
      return parsed?.thumbnails?.["500"] || parsed?.image_url || "/dummy-image.png?height=300&width=300";
    } catch (error) {
      console.warn("Failed to parse cover art data:", error);
      return "/dummy-image.png?height=300&width=300";
    }
  };

  // Navigation handler for tracks
  const handleRecordingClick = (id: string | undefined) => {
    if (id) {
      router.push(`/recording/?id=${id}`);
    }
  };

  // Navigation handler for artist
  const handleArtistClick = (artistId: string | undefined) => {
    if (artistId) {
      router.push(`/artist/?id=${artistId}`);
    }
  };

  const albumTracks = album?.tracks || [];

  if (loading) return <div className="p-8">Loading album...</div>;
  if (error) return <div className="p-8 text-red-500">Error loading album: {error.message}</div>;
  if (!album) return <div className="p-8 text-red-500">Album not found</div>;

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header / Hero Section */}
        <div className="flex flex-col lg:flex-row gap-8 mb-8">
          {/* Album Cover */}
               <div className="relative w-[20rem] h-[20rem] rounded-md overflow-hidden cursor-pointer">
                                    {/* Blurred background */}
                                    <div
                                      className="absolute inset-0 bg-cover bg-center blur-md scale-110"
                                      style={{
                                        backgroundImage: `url(${getCoverArtUrl(album.coverArtData)})`
                                      }}
                                    />
                                    {/* Main image */}
                                    
                                    <Image
                                      src={getCoverArtUrl(album.coverArtData)}
                                      alt={`Album ${album.title || "Unknown"}`}
                                      width={150}
                                      height={150}
                                      className="object-contain w-full h-full relative z-10"
                                    />
                                  </div>

          {/* Album Info */}
          <div className="flex-1 space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Badge variant="outline" className="w-fit">
                  Album
                </Badge>
                <h1 className="text-4xl md:text-5xl font-bold">{album?.title || "-"}</h1>
                {album?.creator?.artistName && (
                  <p className="text-xl text-muted-foreground">
                    by{" "}
                    <span
                      className="text-primary hover:underline cursor-pointer font-medium"
                      onClick={() => handleArtistClick(album.creator?.mbid)}
                    >
                      {album.creator.artistName}
                    </span>
                  </p>
                )}
              </div>

              {/* Description not available in new query structure */}

              <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{album?.releaseDate || "-"}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Music className="w-4 h-4" />
                  <span>{albumTracks.length} tracks</span>
                </div>
                {/* <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>-</span>
                </div> */}
              </div>


            </div>

            <div className="flex flex-wrap gap-3">
              <Button size="lg" className="gap-2" disabled={true}>
                <Play className="w-5 h-5" />
                Play All
              </Button>
            </div>
          </div>
        </div>

        <div 
        // className="grid lg:grid-cols-3 gap-8"
        >
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Tracklist */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Disc className="w-5 h-5" />
                  Tracklist
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {/* Table Header */}
                  <div className="grid grid-cols-12 gap-4 px-4 py-2 text-sm font-medium text-muted-foreground border-b">
                    <div className="col-span-1">#</div>
                    <div className="col-span-6">Title</div>
                    <div className="col-span-2">Duration</div>
                  </div>

                  {/* Track Rows */}
                  {albumTracks.map((track: AlbumTrack, index: number) => {
                    if (!track?.mbid) return null;
                    return (
                      <div
                        key={track.mbid}
                        className="grid grid-cols-12 gap-4 px-4 py-3 rounded-lg hover:bg-muted/50 group cursor-pointer transition-colors"
                        onClick={() => handleRecordingClick(track.mbid)}
                      >
                        <div className="col-span-1 flex items-center">
                          <span className="text-muted-foreground">{index + 1}</span>
                          <Play className="w-4 h-4 hidden opacity-50" />
                        </div>

                        <div className="col-span-6 flex items-center">
                          <div>
                            <p className="font-medium text-primary">{track.title}</p>
                          </div>
                        </div>

                        <div className="col-span-5 flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">
                              {formatDuration(track.duration)}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="opacity-0 group-hover:opacity-100"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent row click when clicking the button
                            }}
                          >
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6"></div>
        </div>
      </div>
    </div>
  )
}
